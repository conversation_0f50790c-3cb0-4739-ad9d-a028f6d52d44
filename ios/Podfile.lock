PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - cunning_document_scanner (1.0.0):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - gal (1.0.0):
    - Flutter
    - FlutterMacOS
  - integration_test (0.0.1):
    - Flutter
  - local_auth_ios (0.0.1):
    - Flutter
  - open_filex (0.0.2):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pdfx (1.0.0):
    - Flutter
  - permission_handler_apple (9.3.0):
    - Flutter
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - ReachabilitySwift (5.2.4)
  - receive_sharing_intent (1.8.1):
    - Flutter
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - Toast (4.1.1)
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - cunning_document_scanner (from `.symlinks/plugins/cunning_document_scanner/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - gal (from `.symlinks/plugins/gal/darwin`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - local_auth_ios (from `.symlinks/plugins/local_auth_ios/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - pdfx (from `.symlinks/plugins/pdfx/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - receive_sharing_intent (from `.symlinks/plugins/receive_sharing_intent/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - ReachabilitySwift
    - SDWebImage
    - SwiftyGif
    - Toast

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  cunning_document_scanner:
    :path: ".symlinks/plugins/cunning_document_scanner/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  gal:
    :path: ".symlinks/plugins/gal/darwin"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  local_auth_ios:
    :path: ".symlinks/plugins/local_auth_ios/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  pdfx:
    :path: ".symlinks/plugins/pdfx/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  receive_sharing_intent:
    :path: ".symlinks/plugins/receive_sharing_intent/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  connectivity_plus: 481668c94744c30c53b8895afb39159d1e619bdf
  cunning_document_scanner: 43a2bda11ef6a33fd68766b287ed056b4caf6a06
  device_info_plus: 335f3ce08d2e174b9fdc3db3db0f4e3b1f66bd89
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 07c75322ede1d47ec9bb4ac82b27c94d3598251a
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  fluttertoast: eaafdac812d1d69931004e881c87a8643b1c9111
  gal: baecd024ebfd13c441269ca7404792a7152fde89
  integration_test: 2d03ab552da9a1f408709a6acf3d7ca4cb3cb307
  local_auth_ios: d3868cf20af969f410fef40b4cf29d72f7fa9876
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  package_info_plus: 566e1b7a2f3900e4b0020914ad3fc051dcc95596
  path_provider_foundation: 608fcb11be570ce83519b076ab6a1fffe2474f05
  pdfx: 77f4dddc48361fbb01486fa2bdee4532cbb97ef3
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  pointer_interceptor_ios: 6c19a55ea369c3f64424c9a95441e2b0e8beb8f3
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  receive_sharing_intent: 222384f00ffe7e952bbfabaa9e3967cb87e5fe00
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  share_plus: de6030e33b4e106470e09322d87cf2a4258d2d1d
  shared_preferences_foundation: 0b09b969fb36da5551c0bc4a2dbd9d0ff9387478
  sqflite: c35dad70033b8862124f8337cc994a809fcd9fa3
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  webview_flutter_wkwebview: daa94b5ed120e19439eb7f797649768d6360ebdd

PODFILE CHECKSUM: 3e1d5ce9d90e93433210629d8d07d17166273967

COCOAPODS: 1.16.2
