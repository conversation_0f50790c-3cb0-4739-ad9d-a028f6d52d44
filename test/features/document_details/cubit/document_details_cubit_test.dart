import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:paperless_mobile/features/document_details/cubit/document_details_cubit.dart';
import 'package:paperless_mobile/core/service/file_service.dart';
import 'package:paperless_mobile/features/notifications/services/local_notification_service.dart';
import 'package:paperless_api/paperless_api.dart';

import 'document_details_cubit_test.mocks.dart';

@GenerateMocks([
  PaperlessDocumentsApi,
  FileService,
  LocalNotificationService,
])
void main() {
  group('DocumentDetailsCubit', () {
    late DocumentDetailsCubit cubit;
    late MockPaperlessDocumentsApi mockDocumentsApi;
    late MockFileService mockFileService;
    late MockLocalNotificationService mockNotificationService;

    setUp(() {
      mockDocumentsApi = MockPaperlessDocumentsApi();
      mockFileService = MockFileService();
      mockNotificationService = MockLocalNotificationService();

      cubit = DocumentDetailsCubit(
        documentsApi: mockDocumentsApi,
        fileService: mockFileService,
        notificationService: mockNotificationService,
      );
    });

    group('_isImageFile', () {
      test('should return true for PNG files', () {
        expect(cubit.isImageFileForTesting('.png'), isTrue);
      });

      test('should return true for JPG files', () {
        expect(cubit.isImageFileForTesting('.jpg'), isTrue);
      });

      test('should return true for JPEG files', () {
        expect(cubit.isImageFileForTesting('.jpeg'), isTrue);
      });

      test('should return true for GIF files', () {
        expect(cubit.isImageFileForTesting('.gif'), isTrue);
      });

      test('should return true for BMP files', () {
        expect(cubit.isImageFileForTesting('.bmp'), isTrue);
      });

      test('should return true for WEBP files', () {
        expect(cubit.isImageFileForTesting('.webp'), isTrue);
      });

      test('should return false for PDF files', () {
        expect(cubit.isImageFileForTesting('.pdf'), isFalse);
      });

      test('should return false for TXT files', () {
        expect(cubit.isImageFileForTesting('.txt'), isFalse);
      });

      test('should return false for DOC files', () {
        expect(cubit.isImageFileForTesting('.doc'), isFalse);
      });

      test('should handle case insensitive extensions', () {
        expect(cubit.isImageFileForTesting('.PNG'), isTrue);
        expect(cubit.isImageFileForTesting('.JPG'), isTrue);
        expect(cubit.isImageFileForTesting('.PDF'), isFalse);
      });
    });
  });
}
