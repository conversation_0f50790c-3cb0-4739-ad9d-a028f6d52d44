import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:gap/gap.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class FullscreenSelectionForm extends StatefulWidget {
  final FocusNode? focusNode;
  final TextEditingController? controller;

  final String hintText;
  final Widget leadingIcon;
  final bool autofocus;
  final String? title;

  final VoidCallback? onTextFieldCleared;
  final List<Widget> trailingActions;
  final Widget Function(BuildContext context, int index) selectionBuilder;
  final int selectionCount;
  final void Function(String value)? onKeyboardSubmit;
  final Widget? floatingActionButton;

  const FullscreenSelectionForm({
    super.key,
    this.title,
    this.focusNode,
    this.controller,
    required this.hintText,
    required this.leadingIcon,
    this.autofocus = true,
    this.onTextFieldCleared,
    this.trailingActions = const [],
    required this.selectionBuilder,
    required this.selectionCount,
    this.onKeyboardSubmit,
    this.floatingActionButton,
  });

  @override
  State<FullscreenSelectionForm> createState() =>
      _FullscreenSelectionFormState();
}

class _FullscreenSelectionFormState extends State<FullscreenSelectionForm> {
  late final FocusNode _focusNode;
  late final TextEditingController _controller;

  bool _showClearIcon = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _controller = (widget.controller ?? TextEditingController())
      ..addListener(() {
        setState(() {
          _showClearIcon = _controller.text.isNotEmpty;
        });
      });
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        //Delay keyboard popup to ensure open animation is finished before.
        Future.delayed(
          const Duration(milliseconds: 200),
          () => _focusNode.requestFocus(),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
        floatingActionButton: widget.floatingActionButton,
        appBar: AppBar(
          iconTheme: const IconThemeData(color: AppColor.primary),
          title: Row(
            children: [
              Text(
                widget.title ?? "Tags",
                style: AppTextStyles.textStyleAppBar,
              ),
            ],
          ),
          actions: [
            if (_showClearIcon)
              IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _controller.clear();
                  widget.onTextFieldCleared?.call();
                },
              ),
            ...widget.trailingActions,
          ],
        ),
        body: Column(
          children: [
            const Gap(24),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              height: 40,
              child: TextFormField(
                // focusNode: _focusNode,
                controller: _controller,
                onFieldSubmitted: (value) {
                  FocusScope.of(context).unfocus();
                  widget.onKeyboardSubmit?.call(value);
                },
                // autofocus: true,
                style: theme.textTheme.bodyLarge?.apply(
                  color: theme.colorScheme.onSurface,
                ),
                decoration: InputDecoration(
                  fillColor: AppColor.white,
                  filled: true,
                  // contentPadding: EdgeInsets.zero,
                  hintStyle: theme.textTheme.bodyLarge?.apply(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  hintText: 'Search here',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide.none,
                  ),
                ),
                textInputAction: TextInputAction.done,
              ),
            ),
            const Gap(16),
            Expanded(
              child: Builder(builder: (context) {
                if (widget.selectionCount == 0) {
                  return Align(
                    alignment: Alignment.topCenter,
                    child: Text(S.of(context)!.noItemsFound).padded(16),
                  );
                }
                return ListView.builder(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemCount: widget.selectionCount,
                  itemBuilder: (BuildContext context, int index) {
                    final highlight =
                        AutocompleteHighlightedOption.of(context) == index;
                    if (highlight) {
                      SchedulerBinding.instance
                          .addPostFrameCallback((Duration timeStamp) {
                        Scrollable.ensureVisible(
                          context,
                          alignment: 0,
                        );
                      });
                    }
                    return widget.selectionBuilder(context, index);
                  },
                );
              }),
            ),
          ],
        ));
  }
}
