import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/features/app_drawer/view/app_drawer.dart';
import 'package:paperless_mobile/features/document_scan/view/scanner_page.dart';
import 'package:paperless_mobile/features/documents/view/pages/documents_page.dart';
import 'package:paperless_mobile/features/folders/view/folder_page.dart';
import 'package:paperless_mobile/features/home/<USER>/bottom_nav_bar_cubit.dart';
import 'package:paperless_mobile/features/home/<USER>/bottom_nav_bar_state.dart';
import 'package:paperless_mobile/features/inbox/view/pages/inbox_page.dart';
import 'package:paperless_mobile/features/labels/view/pages/labels_page.dart';
import 'package:paperless_mobile/features/landing/view/landing_page.dart';
import 'package:paperless_mobile/features/settings/view/settings_page.dart';

class ScaffoldWithNavigationBar extends StatefulWidget {
  final UserModel authenticatedUser;
  final StatefulNavigationShell navigationShell;
  const ScaffoldWithNavigationBar({
    super.key,
    required this.authenticatedUser,
    required this.navigationShell,
  });

  @override
  State<ScaffoldWithNavigationBar> createState() =>
      ScaffoldWithNavigationBarState();
}

class ScaffoldWithNavigationBarState extends State<ScaffoldWithNavigationBar> {
  List<Widget> pages = [
    const LandingPage(),
    const DocumentsPage(),
    const ScannerPage(),
    const InboxPage(),
    const FolderPage(),
    // const LabelsPage(),
    // const SettingsPage()
  ];
  @override
  Widget build(BuildContext context) {
    final BottomNavBarCubit cubit = context.read();
    return BlocBuilder<BottomNavBarCubit, BottomNavBarState>(
      builder: (context, state) {
        return Scaffold(
          drawer: const AppDrawer(),
          bottomNavigationBar: BottomNavigationBar(
            backgroundColor: const Color(0xFFFAFAFC),
            elevation: 0,
            selectedItemColor: AppColor.primary,
            unselectedItemColor: AppColor.black_333333,
            showUnselectedLabels: true,
            type: BottomNavigationBarType.fixed,
            currentIndex: state.indexNavBar,
            onTap: (index) {
              cubit.changedIndexNavBar(index);
            },
            items: [
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  'assets/svgs/home.svg',
                  width: 24,
                ),
                activeIcon: SvgPicture.asset(
                  'assets/svgs/homeActive.svg',
                  width: 24,
                ),
                label: 'Home',
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  'assets/svgs/document.svg',
                  width: 24,
                ),
                activeIcon: SvgPicture.asset(
                  'assets/svgs/document_active.svg',
                  width: 24,
                ),
                label: 'Documents',
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  'assets/svgs/scanner.svg',
                  width: 24,
                ),
                activeIcon: SvgPicture.asset(
                  'assets/svgs/scanner_active.svg',
                  width: 24,
                ),
                label: 'Scanner',
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  'assets/svgs/inbox.svg',
                  width: 24,
                ),
                activeIcon: SvgPicture.asset(
                  'assets/svgs/inbox_active.svg',
                  width: 24,
                ),
                label: 'Inbox',
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  'assets/svgs/folder.svg',
                  width: 24,
                ),
                activeIcon: SvgPicture.asset(
                  'assets/svgs/folder_active.svg',
                  width: 24,
                ),
                label: 'Folders',
              ),
            ],
          ),
          body: SafeArea(
            bottom: false,
            child: pages[state.indexNavBar],
          ),
        );
      },
    );
  }
}
