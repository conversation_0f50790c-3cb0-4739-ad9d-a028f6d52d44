import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';

class DocumentDepartmentWidget extends StatefulWidget {
  const DocumentDepartmentWidget({super.key, required this.documentDetail});

  final DocumentModel documentDetail;

  @override
  State<DocumentDepartmentWidget> createState() =>
      _DocumentDepartmentWidgetState();
}

class _DocumentDepartmentWidgetState extends State<DocumentDepartmentWidget> {
  List<String> department = [];
  late DocumentUploadCubit documentUploadCubit;

  @override
  void initState() {
    super.initState();
    documentUploadCubit = context.read<DocumentUploadCubit>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      initData();
    });
    // initData();
  }

  void initData() {
    final docDepartmentIds = widget.documentDetail.departments;
    final availableDepartments = documentUploadCubit.state.departments;

    for (final deptId in docDepartmentIds) {
      final dept = availableDepartments.firstWhere(
        (d) => d.id == deptId,
        // orElse: () => null,
      );
      department.add(dept.name);
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Departments',
              style: AppTextStyles.textStyle12.copyWith(
                  fontWeight: FontWeight.w500, color: AppColor.grey_909090)),
          const Gap(6),
          Text(department.join(', ') ?? 'None'),
        ],
      ),
    );
  }
}
