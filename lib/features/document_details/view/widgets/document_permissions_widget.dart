import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';

class DocumentPermissionsWidget extends StatefulWidget {
  final DocumentModel documentDetail;
  const DocumentPermissionsWidget({super.key, required this.documentDetail});

  @override
  State<DocumentPermissionsWidget> createState() =>
      _DocumentPermissionsWidgetState();
}

class _DocumentPermissionsWidgetState extends State<DocumentPermissionsWidget> {
  late DocumentUploadCubit documentUploadCubit;
  List<String> userViews = [];
  List<String> userEditors = [];
  List<String> departmentViews = [];
  List<String> departmentEditors = [];

  @override
  initState() {
    super.initState();
    documentUploadCubit = context.read<DocumentUploadCubit>();
    initData();
  }

  void initData() {
    for (int i = 0;
        i < widget.documentDetail.permissions!.view.users.length;
        i++) {
      for (int j = 0; j < documentUploadCubit.state.userViews.length; j++) {
        if (widget.documentDetail.permissions!.view.users[i] ==
            documentUploadCubit.state.userViews[j].id) {
          userViews.add(documentUploadCubit.state.userViews[j].username!);
        }
      }
    }

    for (int i = 0;
        i < widget.documentDetail.permissions!.change.users.length;
        i++) {
      for (int j = 0; j < documentUploadCubit.state.userEditor.length; j++) {
        if (widget.documentDetail.permissions!.change.users[i] ==
            documentUploadCubit.state.userEditor[j].id) {
          userEditors.add(documentUploadCubit.state.userEditor[j].username!);
        }
      }
    }
    for (int i = 0;
        i < widget.documentDetail.permissions!.view.groups.length;
        i++) {
      for (int j = 0; j < documentUploadCubit.state.groupViews.length; j++) {
        if (widget.documentDetail.permissions!.view.groups[i] ==
            documentUploadCubit.state.groupViews[j].id) {
          departmentViews.add(documentUploadCubit.state.groupViews[j].name);
        }
      }
    }
    for (int i = 0;
        i < widget.documentDetail.permissions!.change.groups.length;
        i++) {
      for (int j = 0; j < documentUploadCubit.state.groupEditor.length; j++) {
        if (widget.documentDetail.permissions!.change.groups[i] ==
            documentUploadCubit.state.groupEditor[j].id) {
          departmentEditors.add(documentUploadCubit.state.groupEditor[j].name);
        }
      }
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Owner',
              style: AppTextStyles.textStyle12.copyWith(
                  fontWeight: FontWeight.w500, color: AppColor.grey_909090)),
          const Gap(6),
          Text(widget.documentDetail.owner?.toString() ?? 'Admin'),
          const Gap(16),
          Text('Viewers',
              style: AppTextStyles.textStyle12.copyWith(
                  fontWeight: FontWeight.w500, color: AppColor.grey_909090)),
          const Gap(6),
          Text(
            'Users: ${userViews.join(', ') ?? 'None'}',
          ),
          Text(
            'Groups: ${departmentViews.join(', ') ?? 'None'}',
          ),
          const Gap(16),
          Text('Editors',
              style: AppTextStyles.textStyle12.copyWith(
                  fontWeight: FontWeight.w500, color: AppColor.grey_909090)),
          const Gap(6),
          Text(
            'Users: ${userEditors.join(', ') ?? 'None'}',
          ),
          Text(
            'Groups: ${departmentEditors.join(', ') ?? 'None'}',
          ),
        ],
      ),
    );
  }
}
