// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document_upload_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$DocumentUploadStateCWProxy {
  DocumentUploadState userViews(List<UserPermission> userViews);

  DocumentUploadState userEditor(List<UserPermission> userEditor);

  DocumentUploadState groupEditor(List<Group> groupEditor);

  DocumentUploadState groupViews(List<Group> groupViews);

  DocumentUploadState uploadProgress(double? uploadProgress);

  DocumentUploadState projects(List<Project> projects);

  DocumentUploadState departments(List<Department> departments);

  DocumentUploadState isSelectedPermission(bool isSelectedPermission);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `DocumentUploadState(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// DocumentUploadState(...).copyWith(id: 12, name: "My name")
  /// ````
  DocumentUploadState call({
    List<UserPermission>? userViews,
    List<UserPermission>? userEditor,
    List<Group>? groupEditor,
    List<Group>? groupViews,
    double? uploadProgress,
    List<Project>? projects,
    List<Department>? departments,
    bool? isSelectedPermission,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfDocumentUploadState.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfDocumentUploadState.copyWith.fieldName(...)`
class _$DocumentUploadStateCWProxyImpl implements _$DocumentUploadStateCWProxy {
  const _$DocumentUploadStateCWProxyImpl(this._value);

  final DocumentUploadState _value;

  @override
  DocumentUploadState userViews(List<UserPermission> userViews) =>
      this(userViews: userViews);

  @override
  DocumentUploadState userEditor(List<UserPermission> userEditor) =>
      this(userEditor: userEditor);

  @override
  DocumentUploadState groupEditor(List<Group> groupEditor) =>
      this(groupEditor: groupEditor);

  @override
  DocumentUploadState groupViews(List<Group> groupViews) =>
      this(groupViews: groupViews);

  @override
  DocumentUploadState uploadProgress(double? uploadProgress) =>
      this(uploadProgress: uploadProgress);

  @override
  DocumentUploadState projects(List<Project> projects) =>
      this(projects: projects);

  @override
  DocumentUploadState departments(List<Department> departments) =>
      this(departments: departments);

  @override
  DocumentUploadState isSelectedPermission(bool isSelectedPermission) =>
      this(isSelectedPermission: isSelectedPermission);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `DocumentUploadState(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// DocumentUploadState(...).copyWith(id: 12, name: "My name")
  /// ````
  DocumentUploadState call({
    Object? userViews = const $CopyWithPlaceholder(),
    Object? userEditor = const $CopyWithPlaceholder(),
    Object? groupEditor = const $CopyWithPlaceholder(),
    Object? groupViews = const $CopyWithPlaceholder(),
    Object? uploadProgress = const $CopyWithPlaceholder(),
    Object? projects = const $CopyWithPlaceholder(),
    Object? departments = const $CopyWithPlaceholder(),
    Object? isSelectedPermission = const $CopyWithPlaceholder(),
  }) {
    return DocumentUploadState(
      userViews: userViews == const $CopyWithPlaceholder() || userViews == null
          ? _value.userViews
          // ignore: cast_nullable_to_non_nullable
          : userViews as List<UserPermission>,
      userEditor:
          userEditor == const $CopyWithPlaceholder() || userEditor == null
              ? _value.userEditor
              // ignore: cast_nullable_to_non_nullable
              : userEditor as List<UserPermission>,
      groupEditor:
          groupEditor == const $CopyWithPlaceholder() || groupEditor == null
              ? _value.groupEditor
              // ignore: cast_nullable_to_non_nullable
              : groupEditor as List<Group>,
      groupViews:
          groupViews == const $CopyWithPlaceholder() || groupViews == null
              ? _value.groupViews
              // ignore: cast_nullable_to_non_nullable
              : groupViews as List<Group>,
      uploadProgress: uploadProgress == const $CopyWithPlaceholder()
          ? _value.uploadProgress
          // ignore: cast_nullable_to_non_nullable
          : uploadProgress as double?,
      projects: projects == const $CopyWithPlaceholder() || projects == null
          ? _value.projects
          // ignore: cast_nullable_to_non_nullable
          : projects as List<Project>,
      departments:
          departments == const $CopyWithPlaceholder() || departments == null
              ? _value.departments
              // ignore: cast_nullable_to_non_nullable
              : departments as List<Department>,
      isSelectedPermission:
          isSelectedPermission == const $CopyWithPlaceholder() ||
                  isSelectedPermission == null
              ? _value.isSelectedPermission
              // ignore: cast_nullable_to_non_nullable
              : isSelectedPermission as bool,
    );
  }
}

extension $DocumentUploadStateCopyWith on DocumentUploadState {
  /// Returns a callable class that can be used as follows: `instanceOfDocumentUploadState.copyWith(...)` or like so:`instanceOfDocumentUploadState.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$DocumentUploadStateCWProxy get copyWith =>
      _$DocumentUploadStateCWProxyImpl(this);
}
