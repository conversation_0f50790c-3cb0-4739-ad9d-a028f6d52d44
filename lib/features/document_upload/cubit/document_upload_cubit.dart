import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/bloc/transient_error.dart';
import 'package:paperless_mobile/core/repository/label_repository.dart';
import 'package:paperless_mobile/core/service/connectivity_status_service.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_state.dart';
import 'package:paperless_mobile/features/tasks/model/pending_tasks_notifier.dart';
import 'package:paperless_api/src/models/user_permission.dart';

class DocumentUploadCubit extends Cubit<DocumentUploadState> {
  final PaperlessDocumentsApi _documentApi;
  final PendingTasksNotifier _tasksNotifier;
  final LabelRepository _labelRepository;
  final ConnectivityStatusService _connectivityStatusService;
  final PaperlessTasksApiImpl paperlessTasksApiImpl;

  DocumentUploadCubit(
      this._labelRepository,
      this._documentApi,
      this._connectivityStatusService,
      this._tasksNotifier,
      this.paperlessTasksApiImpl)
      : super(const DocumentUploadState());

  Future<String?> upload(
    Uint8List bytes, {
    required String filename,
    required String title,
    required String userId,
    int? documentType,
    int? correspondent,
    Iterable<int> tags = const [],
    DateTime? createdAt,
    int? asn,
  }) async {
    List<int> projectSelected =
        state.projects.where((e) => e.isSelected).map((e) => e.id).toList();
    List<int> departmentSelected =
        state.departments.where((e) => e.isSelected).map((e) => e.id).toList();
    try {
      final taskId = await _documentApi.create(
        departments: departmentSelected,
        bytes,
        filename: filename,
        title: title,
        correspondent: correspondent,
        documentType: documentType,
        tags: tags,
        createdAt: createdAt,
        asn: asn,
        onProgressChanged: (progress) {
          if (!isClosed) {
            emit(state.copyWith(uploadProgress: progress));
          }
        },
      );
      if (taskId != null) {
        final Map<String, dynamic> param = {
          "documents": [],
          "method": "set_permissions",
          "parameters": {
            "set_permissions": {
              "view": {
                "users": state.userViews
                    .where((e) => e.isSelected)
                    .map((e) => e.id)
                    .toList(),
                "groups": state.groupViews
                    .where((e) => e.isSelected)
                    .map((e) => e.id)
                    .toList(),
              },
              "change": {
                "users": state.userEditor
                    .where((e) => e.isSelected)
                    .map((e) => e.id)
                    .toList(),
                "groups": state.groupEditor
                    .where((e) => e.isSelected)
                    .map((e) => e.id)
                    .toList(),
              }
            }
          }
        };

        _tasksNotifier.listenToTaskChanges(
          taskId,
          param: param,
          projects: projectSelected,
        );
      }
      return taskId;
    } on PaperlessApiException catch (error) {
      addError(TransientPaperlessApiError(
        code: error.code,
        details: error.details,
      ));
    }
    return null;
  }

  Future<void> getUser() async {
    try {
      final response = await _documentApi.getUsers();
      emit(state.copyWith(
          userViews: response,
          userEditor: response.map((e) => e.copyWith()).toList()));
    } catch (e) {
      print(e);
    }
  }

  Future<void> getGroups() async {
    try {
      final response = await _documentApi.getGroups();
      emit(state.copyWith(
          groupViews: response,
          groupEditor: response.map((e) => e.copyWith()).toList()));
    } catch (e) {
      print(e);
    }
  }

  Future<void> selectUserViews(int index) async {
    final List<UserPermission> users =
        List<UserPermission>.from(state.userViews);
    final updatedUser = users[index].copyWith(
      isSelected: !users[index].isSelected,
    );
    users[index] = updatedUser;
    emit(state.copyWith(userViews: users));
  }

  Future<void> selectUserEditor(int index) async {
    final List<UserPermission> users =
        List<UserPermission>.from(state.userEditor);
    final updatedUser = users[index].copyWith(
      isSelected: !users[index].isSelected,
    );
    users[index] = updatedUser;
    emit(state.copyWith(userEditor: users));
  }

  Future<void> selectGroupViews(int index) async {
    final List<Group> groups = List<Group>.from(state.groupViews);
    final updatedGroups = groups[index].copyWith(
      isSelected: !groups[index].isSelected,
    );
    groups[index] = updatedGroups;
    emit(state.copyWith(groupViews: groups));
  }

  Future<void> selectGroupEditor(int index) async {
    final List<Group> groups = List<Group>.from(state.groupEditor);
    final updatedGroups = groups[index].copyWith(
      isSelected: !groups[index].isSelected,
    );
    groups[index] = updatedGroups;
    emit(state.copyWith(groupEditor: groups));
  }

  Future<void> getAllProject() async {
    try {
      final response = await _documentApi.getAllProject();
      emit(state.copyWith(projects: response.results));
    } catch (e) {
      print(e);
    }
  }

  Future<void> selectedProject(int index) async {
    final List<Project> projects = List<Project>.from(state.projects);

    for (int i = 0; i < projects.length; i++) {
      projects[i] = projects[i]
          .copyWith(isSelected: i == index ? !projects[i].isSelected : false);
    }

    emit(state.copyWith(projects: projects));
  }

  Future<void> initDataProject(List<int> selectedIds) async {
    await getAllProject();

    final updatedProjects = state.projects.map((project) {
      return project.copyWith(
        isSelected: selectedIds.contains(project.id),
      );
    }).toList();

    emit(state.copyWith(projects: updatedProjects));
  }

  Future<void> addProject(String id) async {
    List<int> projectIds = [];
    for (var element in state.projects) {
      if (element.isSelected == true) {
        projectIds.add(element.id);
      }
    }
    try {
      paperlessTasksApiImpl.addProject(projectIds, id);
    } catch (e) {
      print(e);
    }
  }

  Future<void> initData({
    required List<int> userViews,
    required List<int> userEditor,
    required List<int> groupViews,
    required List<int> groupEditor,
    required List<int> departments,
  }) async {
    await getUser();
    await getGroups();
    await getAllDepartment();

    final updatedUserViews = state.userViews.map((user) {
      return user.copyWith(
        isSelected: userViews.contains(user.id),
      );
    }).toList();

    final updatedUserEditors = state.userEditor.map((user) {
      return user.copyWith(
        isSelected: userEditor.contains(user.id),
      );
    }).toList();

    final updatedGroupViews = state.groupViews.map((group) {
      return group.copyWith(
        isSelected: groupViews.contains(group.id),
      );
    }).toList();

    final updatedGroupEditors = state.groupEditor.map((group) {
      return group.copyWith(
        isSelected: groupEditor.contains(group.id),
      );
    }).toList();

    final updatedDepartments = state.departments.map((department) {
      return department.copyWith(
        isSelected: departments.contains(department.id),
      );
    }).toList();

    emit(state.copyWith(
        userViews: updatedUserViews,
        userEditor: updatedUserEditors,
        groupViews: updatedGroupViews,
        groupEditor: updatedGroupEditors,
        departments: updatedDepartments));
  }

  Future<void> addPermission(String id) async {
    final Map<String, dynamic> param = {
      "documents": [id],
      "method": "set_permissions",
      "parameters": {
        "set_permissions": {
          "view": {
            "users": state.userViews
                .where((e) => e.isSelected)
                .map((e) => e.id)
                .toList(),
            "groups": state.groupViews
                .where((e) => e.isSelected)
                .map((e) => e.id)
                .toList(),
          },
          "change": {
            "users": state.userEditor
                .where((e) => e.isSelected)
                .map((e) => e.id)
                .toList(),
            "groups": state.groupEditor
                .where((e) => e.isSelected)
                .map((e) => e.id)
                .toList(),
          }
        }
      }
    };
    try {
      paperlessTasksApiImpl.addPermission(param);
    } catch (e) {
      print(e);
    }
  }

  Future<void> getAllDepartment() async {
    try {
      final response = await _documentApi.getAllDepartments();
      emit(state.copyWith(departments: response.results));
      print('----------------------');
      print(response.results.length);
    } catch (e) {
      print(e);
    }
  }

  Future<void> selectedDepartment(int index) async {
    final List<Department> departments =
        List<Department>.from(state.departments);
    final updatedDepartment = departments[index].copyWith(
      isSelected: !departments[index].isSelected,
    );
    departments[index] = updatedDepartment;
    emit(state.copyWith(departments: departments));
  }

  Future<void> checkSelectedPermission() async {
    final selectedUserViews = state.userViews.where((e) => e.isSelected);
    final selectedUserEditor = state.userEditor.where((e) => e.isSelected);
    final selectedGroupViews = state.groupViews.where((e) => e.isSelected);
    final selectedGroupEditor = state.groupEditor.where((e) => e.isSelected);
    if (selectedUserViews.isNotEmpty ||
        selectedUserEditor.isNotEmpty ||
        selectedGroupViews.isNotEmpty ||
        selectedGroupEditor.isNotEmpty) {
      emit(state.copyWith(isSelectedPermission: true));
    } else {
      emit(state.copyWith(isSelectedPermission: false));
    }
  }

  Future<void> updateDepartment(String id, String name) async {
    try {
      await _documentApi.updateDepartment(id, name);
    } catch (e) {
      print(e);
    }
  }

  Future<void> createDepartment(String name) async {
    try {
      await _documentApi.createDepartment(name);
    } catch (e) {
      print(e);
    }
  }

  Future<void> deleteDepartment(String id) async {
    try {
      await _documentApi.deleteDepartment(id);
    } catch (e) {
      print(e);
    }
  }

  Future<void> searchDepartment(String query) async {
    try {
      final response = await _documentApi.searchDepartment(query);
      emit(state.copyWith(departments: response.results));
    } catch (e) {
      print(e);
    }
  }
}
