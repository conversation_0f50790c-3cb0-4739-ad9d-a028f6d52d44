import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:equatable/equatable.dart';
import 'package:paperless_api/paperless_api.dart';

part 'document_upload_state.g.dart';

@CopyWith()
class DocumentUploadState extends Equatable {
  final double? uploadProgress;
  final List<UserPermission> userViews;
  final List<UserPermission> userEditor;
  final List<Group> groupViews;
  final List<Group> groupEditor;
  final List<Project> projects;
  final List<Department> departments;
  final bool isSelectedPermission;

  const DocumentUploadState({
    this.userViews = const [],
    this.userEditor = const [],
    this.groupEditor = const [],
    this.groupViews = const [],
    this.uploadProgress,
    this.projects = const [],
    this.departments = const [],
    this.isSelectedPermission = false,
  });

  @override
  List<Object?> get props => [
        userViews,
        uploadProgress,
        userEditor,
        groupEditor,
        groupViews,
        projects,
        departments,
        isSelectedPermission,
      ];
}
