import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/repository/label_repository.dart';
import 'package:paperless_mobile/features/document_bulk_action/cubit/document_bulk_action_cubit.dart';
import 'package:paperless_mobile/features/document_bulk_action/view/widgets/fullscreen_bulk_edit_label_page.dart';
import 'package:paperless_mobile/features/document_bulk_action/view/widgets/fullscreen_bulk_edit_tags_widget.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/routing/routes/documents_route.dart';

class BulkDocumentPage extends StatelessWidget {
  const BulkDocumentPage({super.key, required this.$extra});
  final BulkEditExtraWrapper $extra;

  @override
  Widget build(BuildContext context) {
    final labelRepository = context.read<LabelRepository>();

    return BlocProvider(
      create: (_) => DocumentBulkActionCubit(
        context.read(),
        context.read(),
        selection: $extra.selection,
      ),
      child: BlocBuilder<DocumentBulkActionCubit, DocumentBulkActionState>(
        builder: (context, state) {
          return switch ($extra.type) {
            LabelType.tag => const FullscreenBulkEditTagsWidget(),
            _ => FullscreenBulkEditLabelPage(
                title: switch ($extra.type) {
                  LabelType.correspondent => S.of(context)!.correspondent,
                  LabelType.documentType => S.of(context)!.documentType,
                  LabelType.storagePath => S.of(context)!.storagePath,
                  _ => throw Exception("Parameter not allowed here."),
                },
                options: switch ($extra.type) {
                  LabelType.correspondent => labelRepository.correspondents,
                  LabelType.documentType => labelRepository.documentTypes,
                  LabelType.storagePath => labelRepository.storagePaths,
                  _ => throw Exception("Parameter not allowed here."),
                },
                selection: state.selection,
                labelMapper: (document) {
                  return switch ($extra.type) {
                    LabelType.correspondent => document.correspondent,
                    LabelType.documentType => document.documentType,
                    LabelType.storagePath => document.storagePath,
                    _ => throw Exception("Parameter not allowed here."),
                  };
                },
                leadingIcon: switch ($extra.type) {
                  LabelType.correspondent => const Icon(Icons.person_outline),
                  LabelType.documentType =>
                    const Icon(Icons.description_outlined),
                  LabelType.storagePath => const Icon(Icons.folder_outlined),
                  _ => throw Exception("Parameter not allowed here."),
                },
                hintText: S.of(context)!.startTyping,
                onSubmit: switch ($extra.type) {
                  LabelType.correspondent => context
                      .read<DocumentBulkActionCubit>()
                      .bulkModifyCorrespondent,
                  LabelType.documentType => context
                      .read<DocumentBulkActionCubit>()
                      .bulkModifyDocumentType,
                  LabelType.storagePath => context
                      .read<DocumentBulkActionCubit>()
                      .bulkModifyStoragePath,
                  _ => throw Exception("Parameter not allowed here."),
                },
                assignMessageBuilder: (int count, String name) {
                  return switch ($extra.type) {
                    LabelType.correspondent => S
                        .of(context)!
                        .bulkEditCorrespondentAssignMessage(name, count),
                    LabelType.documentType => S
                        .of(context)!
                        .bulkEditDocumentTypeAssignMessage(count, name),
                    LabelType.storagePath => S
                        .of(context)!
                        .bulkEditDocumentTypeAssignMessage(count, name),
                    _ => throw Exception("Parameter not allowed here."),
                  };
                },
                removeMessageBuilder: (int count) {
                  return switch ($extra.type) {
                    LabelType.correspondent =>
                      S.of(context)!.bulkEditCorrespondentRemoveMessage(count),
                    LabelType.documentType =>
                      S.of(context)!.bulkEditDocumentTypeRemoveMessage(count),
                    LabelType.storagePath =>
                      S.of(context)!.bulkEditStoragePathRemoveMessage(count),
                    _ => throw Exception("Parameter not allowed here."),
                  };
                },
              ),
          };
        },
      ),
    );
  }
}
