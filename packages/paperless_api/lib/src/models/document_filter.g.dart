// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document_filter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DocumentFilterAdapter extends TypeAdapter<DocumentFilter> {
  @override
  final int typeId = 128;

  @override
  DocumentFilter read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DocumentFilter(
      documentType: fields[2] as IdQueryParameter,
      correspondent: fields[3] as IdQueryParameter,
      storagePath: fields[4] as IdQueryParameter,
      asnQuery: fields[5] as IdQueryParameter,
      tags: fields[6] as TagsQuery,
      sortField: fields[7] as SortField?,
      sortOrder: fields[8] as SortOrder,
      page: fields[1] as int,
      pageSize: fields[0] as int,
      query: fields[12] as TextQuery,
      added: fields[10] as DateRangeQuery,
      created: fields[9] as DateRangeQuery,
      modified: fields[11] as DateRangeQuery,
      moreLike: fields[13] as int?,
      selectedView: fields[14] as int?,
      projectsIdIn: (fields[15] as List?)?.cast<int>() ?? const [],
      departmentIdIn: (fields[16] as List?)?.cast<int>() ?? const [],
    );
  }

  @override
  void write(BinaryWriter writer, DocumentFilter obj) {
    writer
      ..writeByte(17)
      ..writeByte(0)
      ..write(obj.pageSize)
      ..writeByte(1)
      ..write(obj.page)
      ..writeByte(2)
      ..write(obj.documentType)
      ..writeByte(3)
      ..write(obj.correspondent)
      ..writeByte(4)
      ..write(obj.storagePath)
      ..writeByte(5)
      ..write(obj.asnQuery)
      ..writeByte(6)
      ..write(obj.tags)
      ..writeByte(7)
      ..write(obj.sortField)
      ..writeByte(8)
      ..write(obj.sortOrder)
      ..writeByte(9)
      ..write(obj.created)
      ..writeByte(10)
      ..write(obj.added)
      ..writeByte(11)
      ..write(obj.modified)
      ..writeByte(12)
      ..write(obj.query)
      ..writeByte(13)
      ..write(obj.moreLike)
      ..writeByte(14)
      ..write(obj.selectedView)
      ..writeByte(15)
      ..write(obj.projectsIdIn)
      ..writeByte(16)
      ..write(obj.departmentIdIn);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DocumentFilterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
