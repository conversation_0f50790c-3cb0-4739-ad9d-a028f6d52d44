// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group.g.dart';

@JsonSerializable()
class Group extends Equatable {
  int id;
  String name;
  List<String> permissions;
  bool isSelected;
  Group({
    required this.id,
    required this.name,
    required this.permissions,
    this.isSelected = false,
  });

  factory Group.fromJson(Map<String, dynamic> json) =>
      _$GroupFromJson(json);

  Map<String, dynamic> toJson() => _$GroupToJson(this);

  @override
  // TODO: implement props
  List<Object?> get props => [id, name, permissions, isSelected];

  Group copyWith({
    int? id,
    String? name,
    List<String>? permissions,
    bool? isSelected,
  }) {
    return Group(
      id: id ?? this.id,
      name: name ?? this.name,
      permissions: permissions ?? this.permissions,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
