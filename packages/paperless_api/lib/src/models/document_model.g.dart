// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DocumentModel _$DocumentModelFromJson(Map<String, dynamic> json) =>
    DocumentModel(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
      content: json['content'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => (e as num).toInt()) ??
          const <int>[],
      documentType: (json['document_type'] as num?)?.toInt(),
      correspondent: (json['correspondent'] as num?)?.toInt(),
      created: const LocalDateTimeJsonConverter()
          .fromJson(json['created'] as String),
      modified: const LocalDateTimeJsonConverter()
          .fromJson(json['modified'] as String),
      added:
          const LocalDateTimeJsonConverter().fromJson(json['added'] as String),
      archiveSerialNumber: (json['archive_serial_number'] as num?)?.toInt(),
      originalFileName: json['original_file_name'] as String?,
      archivedFileName: json['archived_file_name'] as String?,
      storagePath: (json['storage_path'] as num?)?.toInt(),
      searchHit: json['__search_hit__'] == null
          ? null
          : SearchHit.fromJson(json['__search_hit__'] as Map<String, dynamic>),
      owner: (json['owner'] as num?)?.toInt(),
      userCanChange: json['user_can_change'] as bool?,
      permissions: json['permissions'] == null
          ? null
          : DocumentPermission.fromJson(
              json['permissions'] as Map<String, dynamic>),
      customFields: (json['custom_fields'] as List<dynamic>?)?.map(
              (e) => CustomFieldInstance.fromJson(e as Map<String, dynamic>)) ??
          const [],
      notes: (json['notes'] as List<dynamic>?)
              ?.map((e) => NoteModel.fromJson(e as Map<String, dynamic>)) ??
          const [],
      projects: (json['projects'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      departments: (json['departments'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
    );

Map<String, dynamic> _$DocumentModelToJson(DocumentModel instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'title': instance.title,
    'content': instance.content,
    'tags': instance.tags.toList(),
    'document_type': instance.documentType,
    'correspondent': instance.correspondent,
    'storage_path': instance.storagePath,
    'created': const LocalDateTimeJsonConverter().toJson(instance.created),
    'modified': const LocalDateTimeJsonConverter().toJson(instance.modified),
    'added': const LocalDateTimeJsonConverter().toJson(instance.added),
    'archive_serial_number': instance.archiveSerialNumber,
    'original_file_name': instance.originalFileName,
    'archived_file_name': instance.archivedFileName,
    'projects': instance.projects,
    'permissions': instance.permissions,
    'departments': instance.departments,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('__search_hit__', instance.searchHit);
  val['owner'] = instance.owner;
  val['user_can_change'] = instance.userCanChange;
  val['notes'] = instance.notes.toList();
  val['custom_fields'] = instance.customFields.toList();
  return val;
}

DocumentPermission _$DocumentPermissionFromJson(Map<String, dynamic> json) =>
    DocumentPermission(
      view: UserAndGroup.fromJson(json['view'] as Map<String, dynamic>),
      change: UserAndGroup.fromJson(json['change'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DocumentPermissionToJson(DocumentPermission instance) =>
    <String, dynamic>{
      'view': instance.view,
      'change': instance.change,
    };

UserAndGroup _$UserAndGroupFromJson(Map<String, dynamic> json) => UserAndGroup(
      users: (json['users'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      groups: (json['groups'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$UserAndGroupToJson(UserAndGroup instance) =>
    <String, dynamic>{
      'users': instance.users,
      'groups': instance.groups,
    };
